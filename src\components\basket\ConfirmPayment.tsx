import React, { useState } from "react";
import { CheckSquare, Square } from "react-feather";
import { <PERSON><PERSON>, ModalContent, ModalBody } from "@heroui/react";
import { But<PERSON> } from "../ui/button";
import { updateOrder } from "../../services/ordersServices";
import { useCurrentUser } from "../../hooks/useCurrentUser";
import { getCompleteSellerInfo } from "../../services/sellerService";
import { toast } from "sonner";

interface BasketItem {
  id: number;
  orderId?: string; // Original Firebase document ID for updates
  title: string;
  time: string;
  subtotal: number;
  image: string;
  userName: string;
  profileId?: string; // Seller's user ID
}

interface ConfirmPaymentProps {
  selectedItem: BasketItem | null;
  onConfirm: () => void;
  currencySymbol?: string; // Added currency symbol prop
}

const ConfirmPayment: React.FC<ConfirmPaymentProps> = ({
  selectedItem,
  onConfirm,
  currencySymbol = "$", // Default to $ if not provided
}) => {
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [checked, setChecked] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const { user, isAuthenticated } = useCurrentUser();

  if (!selectedItem) return null;

  const transactionFee = selectedItem.subtotal * 0.04;
  const total = selectedItem.subtotal + transactionFee;

  const handlePaymentConfirm = async () => {
    if (!isAuthenticated || !user) {
      toast.error("Please log in to proceed with payment");
      return;
    }

    if (!selectedItem?.profileId) {
      toast.error("Seller information not available");
      return;
    }

    setIsProcessing(true);

    try {
      // Get seller information
      const sellerResult = await getCompleteSellerInfo(selectedItem.profileId);

      if (!sellerResult.success || !sellerResult.seller) {
        toast.error("Unable to retrieve seller information");
        setIsProcessing(false);
        return;
      }

      const seller = sellerResult.seller;

      if (!seller.stripeAccountId) {
        toast.error("Seller has not completed payment setup");
        setIsProcessing(false);
        return;
      }

      // Prepare escrow data
      const escrowData = {
        userId: user.uid,
        userEmail: user.email || "",
        sellerId: selectedItem.profileId,
        sellerEmail: seller.userEmail || "",
        sellerStripeAccountId: seller.stripeAccountId,
        orderId: selectedItem.orderId || selectedItem.id.toString(),
        amount: Math.round(total * 100), // Convert to cents
        currency: "usd", // You might want to make this dynamic based on user preference
        productName: selectedItem.title,
        productDescription: `Service order: ${selectedItem.title}`,
      };

      // Call the escrow API
      const response = await fetch("/api/escrow/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(escrowData),
      });

      const result = await response.json();

      if (result.success && result.checkoutUrl) {
        // Update order status
        if (selectedItem?.orderId || selectedItem?.id) {
          await updateOrder(selectedItem.orderId || selectedItem.id.toString(), {
            status: "NEW",
            transactionId: result.transactionId,
          });
        }

        // Redirect to Stripe checkout
        window.location.href = result.checkoutUrl;
      } else {
        toast.error(result.error || "Failed to create escrow payment");
        setIsProcessing(false);
      }
    } catch (error) {
      console.error("Error creating escrow payment:", error);
      toast.error("An error occurred while processing payment");
      setIsProcessing(false);
    }
  };

  return (
    <div className="flex flex-col mr-4">
      <div className="flex justify-between">
        <p className="text-[#404040]">Subtotal</p>
        <p className="font-bold text-primary">
          {currencySymbol}
          {selectedItem.subtotal.toFixed(2)}
        </p>
      </div>
      <div className="flex justify-between my-1">
        <p className="text-[#404040]">Transaction fee (4%)</p>
        <p className="font-bold text-primary">
          {currencySymbol}
          {transactionFee.toFixed(2)}
        </p>
      </div>
      <div className="flex justify-between mb-4">
        <p className="text-[#404040]">Order total</p>
        <p className="font-bold text-primary">
          {currencySymbol}
          {total.toFixed(2)}
        </p>
      </div>

      <div className="flex justify-between text-subtitle mb-2">
        <p>Delivery time</p>
        <p className="font-semibold">{selectedItem.time}</p>
      </div>

      <div className="flex flex-row gap-2 mt-2 border-b-2 pb-3">
        <div onClick={() => setChecked((prev) => !prev)} className="cursor-pointer select-none">
          {checked ? <CheckSquare color="#333333" /> : <Square color="#bdbdbd" />}
        </div>
        <div>
          <p className="text-primary">Request an invoice </p>
          <p className="text-[#898887]">
            Note: to obtain an Invoice you'll need to provide your tax details (legal name, address
            and VAT registration number).
          </p>
        </div>
      </div>
      <div className="mt-3">
        <p className="text-subtitle">
          Terms: By placing your order, you confirm that you agree to the User Terms and Conditions.
        </p>
        <button
          onClick={() => setIsPaymentModalOpen(true)}
          className="btn-xs text-white btn py-4 w-full bg-primary rounded-full mt-2"
        >
          Confirm Payment
        </button>
      </div>

      <Modal
        placement="auto"
        isOpen={isPaymentModalOpen}
        onClose={() => setIsPaymentModalOpen(false)}
        hideCloseButton={true}
      >
        <ModalContent className="modal-content w-80 p-12 rounded-3xl">
          {() => (
            <>
              <ModalBody>
                <p className="text-center text-black text-lg">
                  Confirm payment of {currencySymbol}
                  {total.toFixed(2)}?
                </p>
                <div>
                  <Button
                    variant="outline"
                    className="rounded-full w-full mt-5 border-black text-black border-2 py-5 text-base"
                    onClick={handlePaymentConfirm}
                  >
                    Yes, proceed with payment
                  </Button>
                  <Button
                    variant="outline"
                    className="rounded-full w-full mt-3 border-black text-black border-2 py-5 text-base"
                    onClick={() => setIsPaymentModalOpen(false)}
                  >
                    No, cancel
                  </Button>
                </div>
              </ModalBody>
            </>
          )}
        </ModalContent>
      </Modal>
    </div>
  );
};

export default ConfirmPayment;
